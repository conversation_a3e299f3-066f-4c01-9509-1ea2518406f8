@use './styles/variables.scss' as *;
.container {
  margin: 0;
  padding-top: calc($padding-top-container + 40px); // Add space for custom title bar
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.logo {
  height: $logo-height;
  padding: $padding-logo;
  will-change: filter;
  transition: $transition-filter;

  &.vite:hover {
    filter: $drop-shadow-vite;
  }

  &.react:hover {
    filter: $drop-shadow-react;
  }

  &.tauri:hover {
    filter: $drop-shadow-tauri;
  }
}

.row {
  display: flex;
  justify-content: center;
}

.link {
  font-weight: $font-weight-medium;
  color: $primary-color;
  text-decoration: inherit;

  &:hover {
    color: $primary-hover;
  }

  @media (prefers-color-scheme: dark) {
    &:hover {
      color: $tauri-color;
    }
  }
}

.title {
  text-align: center;
}

.input,
.button {
  border-radius: $border-radius;
  border: $border-default;
  padding: $padding-base;
  font-size: 1em;
  font-weight: $font-weight-medium;
  font-family: inherit;
  color: $text-light;
  background-color: $input-bg-light;
  transition: $transition-border;
  box-shadow: $box-shadow;
  outline: none;

  @media (prefers-color-scheme: dark) {
    color: $text-dark;
    background-color: $input-bg-dark;
  }
}

.button {
  cursor: pointer;

  &:hover {
    border-color: $border-hover;
  }

  &:active {
    border-color: $border-hover;
    background-color: $input-active-light;

    @media (prefers-color-scheme: dark) {
      background-color: $input-active-dark;
    }
  }
}

.greetInput {
  margin-right: $margin-input;
}
