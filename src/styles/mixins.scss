// SCSS Mixins for reusable styles
@import "./variables.scss";

// Button mixin with variants
@mixin button-base {
  border-radius: $border-radius;
  border: $border-default;
  padding: $padding-base;
  font-size: 1em;
  font-weight: $font-weight-medium;
  font-family: inherit;
  cursor: pointer;
  transition: $transition-border;
  outline: none;
}

@mixin button-primary {
  @include button-base;
  background-color: $primary-color;
  color: white;

  &:hover {
    background-color: $primary-hover;
  }
}

@mixin button-secondary {
  @include button-base;
  background-color: transparent;
  color: $primary-color;
  border: 1px solid $primary-color;

  &:hover {
    background-color: $primary-color;
    color: white;
  }
}

// Card mixin
@mixin card {
  padding: 2rem;
  border: 1px solid #e0e0e0;
  border-radius: $border-radius;
  background-color: $input-bg-light;
  box-shadow: $box-shadow;

  @media (prefers-color-scheme: dark) {
    background-color: $input-bg-dark;
    border-color: #404040;
  }
}

// Responsive breakpoints
@mixin mobile {
  @media (max-width: 768px) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: 769px) and (max-width: 1024px) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: 1025px) {
    @content;
  }
}
