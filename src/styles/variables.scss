// Global SCSS Variables
$primary-color: #646cff;
$primary-hover: #535bf2;
$tauri-color: #24c8db;
$vite-color: #747bff;
$react-color: #61dafb;

// Typography
$font-family: Inter, Avenir, Helvetica, Arial, sans-serif;
$font-size-base: 16px;
$line-height-base: 24px;
$font-weight-normal: 400;
$font-weight-medium: 500;

// Colors
$text-light: #0f0f0f;
$bg-light: #f6f6f6;
$text-dark: #f6f6f6;
$bg-dark: #2f2f2f;
$input-bg-light: #ffffff;
$input-bg-dark: #0f0f0f98;
$input-active-dark: #0f0f0f69;
$input-active-light: #e8e8e8;

// Spacing
$padding-base: 0.6em 1.2em;
$margin-input: 5px;
$padding-logo: 1.5em;
$padding-top-container: 10vh;

// Border
$border-radius: 8px;
$border-default: 1px solid transparent;
$border-hover: #396cd8;

// Shadows
$box-shadow: 0 2px 2px rgba(0, 0, 0, 0.2);
$drop-shadow-vite: drop-shadow(0 0 2em $vite-color);
$drop-shadow-react: drop-shadow(0 0 2em $react-color);
$drop-shadow-tauri: drop-shadow(0 0 2em $tauri-color);

// Transitions
$transition-filter: 0.75s;
$transition-border: border-color 0.25s;

// Logo size
$logo-height: 6em;
