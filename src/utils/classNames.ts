/**
 * Utility function for combining CSS class names
 * Useful for working with SCSS modules and conditional classes
 */
export const classNames = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

/**
 * Utility function for combining SCSS module classes with conditional logic
 * @param baseClasses - Object containing SCSS module classes
 * @param conditionalClasses - Array of conditional class applications
 */
export const combineModuleClasses = (
  baseClasses: Record<string, string>,
  conditionalClasses: Array<{
    className: keyof typeof baseClasses;
    condition: boolean;
  }>
): string => {
  const classes = conditionalClasses
    .filter(({ condition }) => condition)
    .map(({ className }) => baseClasses[className])
    .filter(<PERSON>olean);
  
  return classes.join(' ');
};

/**
 * Example usage:
 * 
 * import styles from './Component.module.scss';
 * import { classNames, combineModuleClasses } from '../utils/classNames';
 * 
 * // Simple combination
 * const className = classNames(styles.button, styles.primary, isActive && styles.active);
 * 
 * // Complex conditional logic
 * const className = combineModuleClasses(styles, [
 *   { className: 'button', condition: true },
 *   { className: 'primary', condition: isPrimary },
 *   { className: 'active', condition: isActive },
 *   { className: 'disabled', condition: isDisabled }
 * ]);
 */
