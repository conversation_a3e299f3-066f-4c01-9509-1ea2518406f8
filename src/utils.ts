


export function getTranslation(scope = "tauri-app") {
  return (key: string, placeholders?: Record<string, any>) => {
    let scopedKey = key
    if (scope) {
      scopedKey = `${scope}.${key}`
    }

    const lng = window.__LANG__
    let translateStr = lng[scopedKey] || lng[key] || key
    if (placeholders) {
      Object.entries(placeholders).forEach(([k, v]) => {
        translateStr = translateStr.replace(`{{${k}}}`, v)
      })
    }
    return translateStr
  }
}
