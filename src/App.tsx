import { useEffect, useState } from "react";
import reactLogo from "./assets/react.svg";
import { invoke } from "@tauri-apps/api/core";
// import { restoreStateCurrent, saveWindowState } from "@tauri-apps/plugin-window-state";
import styles from "./App.module.scss";
import ExampleComponent from "./components/ExampleComponent";
import CustomTitleBar from "./components/CustomTitleBar";
import DraggableAreaExample from "./components/DraggableAreaExample";
import { getTranslation } from "./utils";

function App() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");
  const t = getTranslation()

  useEffect(() => {
    setupAppWindow()
  }, [])

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    let x = await invoke("call_plugin", {
      meta: {
        id: "extract-mp3",
        method: "greet",
        payload: {
          name: "ZZHZ"
        }
      }
    })
    console.log(x)
    // setGreetMsg(await invoke("greet", { name }));
  }

  async function setupAppWindow() {
    const appWindow = (await import('@tauri-apps/api/window')).getCurrentWindow();
    
    // Restore window state before showing the window
    // await restoreStateCurrent();
    
    // Show the window after restoring state
    appWindow.show();
    
    // Set up window close event listener to save state
    // const unlisten = await appWindow.onCloseRequested(async () => {
    //   // Save window state before closing
    //   // await saveWindowState();
    // });

    // Clean up listener on component unmount
    // return () => {
    //   unlisten();
    // };
  }

  return (
    <>
      {/* <CustomTitleBar title="Tauri + React App" /> */}
      <main className={styles.container}>
        <h1 className={styles.title}>Welcome to Tauri + React {t('content')}</h1>

        <div className={styles.row}>
          <a href="https://vite.dev" target="_blank" className={styles.link}>
            <img src="/vite.svg" className={`${styles.logo} ${styles.vite}`} alt="Vite logo" />
          </a>
          <a href="https://tauri.app" target="_blank" className={styles.link}>
            <img src="/tauri.svg" className={`${styles.logo} ${styles.tauri}`} alt="Tauri logo" />
          </a>
          <a href="https://react.dev" target="_blank" className={styles.link}>
            <img src={reactLogo} className={`${styles.logo} ${styles.react}`} alt="React logo" />
          </a>
        </div>
        <p>Click on the Tauri, Vite, and React logos to learn more.</p>

        <form
          className={styles.row}
          onSubmit={(e) => {
            e.preventDefault();
            greet();
          }}
        >
          <input
            id="greet-input"
            className={`${styles.input} ${styles.greetInput}`}
            onChange={(e) => setName(e.currentTarget.value)}
            placeholder="Enter a name..."
          />
          <button type="submit" className={styles.button}>Greet</button>
        </form>
        <p>{greetMsg}</p>

        <ExampleComponent
          title="SCSS Modules Demo"
          description="This component demonstrates SCSS modules with scoped styling and shared variables."
        />

        <DraggableAreaExample />
      </main>
    </>
  );
}

export default App;
