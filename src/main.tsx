import React from "react";
import ReactDOM from "react-dom/client";
import App from "./App";
import "./styles/global.scss";
import { invoke } from "@tauri-apps/api/core";

async function main() {
  window.__LANG__ = await invoke("get_lang", { code: "en" })
  ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
    <React.StrictMode>
      <App />
    </React.StrictMode>,
  );
}

main();
