import React from 'react';
import styles from './ExampleComponent.module.scss';

interface ExampleComponentProps {
  title: string;
  description?: string;
}

const ExampleComponent: React.FC<ExampleComponentProps> = ({ title, description }) => {
  return (
    <div className={styles.container}>
      <h2 className={styles.title}>{title}</h2>
      {description && (
        <p className={styles.description}>{description}</p>
      )}
      <button className={`${styles.button} ${styles.primary}`}>
        Primary Button
      </button>
      <button className={`${styles.button} ${styles.secondary}`}>
        Secondary Button
      </button>
    </div>
  );
};

export default ExampleComponent;
