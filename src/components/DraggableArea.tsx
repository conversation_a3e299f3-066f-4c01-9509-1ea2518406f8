import React from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import styles from './DraggableArea.module.scss';

interface DraggableAreaProps {
  children?: React.ReactNode;
  className?: string;
  height?: string;
}

const DraggableArea: React.FC<DraggableAreaProps> = ({ 
  children, 
  className = '', 
  height = '40px' 
}) => {
  const handleMouseDown = async (e: React.MouseEvent) => {
    // Prevent dragging if clicking on interactive elements
    const target = e.target as HTMLElement;
    if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.closest('button')) {
      return;
    }

    try {
      const appWindow = getCurrentWindow();
      await appWindow.startDragging();
    } catch (error) {
      console.error('Failed to start window dragging:', error);
    }
  };

  return (
    <div 
      className={`${styles.draggableArea} ${className}`}
      style={{ height }}
      onMouseDown={handleMouseDown}
      data-tauri-drag-region
    >
      {children}
    </div>
  );
};

export default DraggableArea;