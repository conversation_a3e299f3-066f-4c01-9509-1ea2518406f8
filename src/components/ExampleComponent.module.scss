@use '../styles/variables.scss' as *;

.container {
  padding: 2rem;
  border: 1px solid #e0e0e0;
  border-radius: $border-radius;
  margin: 1rem 0;
  background-color: $input-bg-light;
  box-shadow: $box-shadow;

  @media (prefers-color-scheme: dark) {
    background-color: $input-bg-dark;
    border-color: #404040;
  }
}

.title {
  color: $primary-color;
  font-size: 1.5rem;
  font-weight: $font-weight-medium;
  margin-bottom: 1rem;

  @media (prefers-color-scheme: dark) {
    color: $tauri-color;
  }
}

.description {
  color: $text-light;
  margin-bottom: 1.5rem;
  line-height: 1.6;

  @media (prefers-color-scheme: dark) {
    color: $text-dark;
  }
}

.button {
  border-radius: $border-radius;
  border: $border-default;
  padding: $padding-base;
  font-size: 1em;
  font-weight: $font-weight-medium;
  font-family: inherit;
  cursor: pointer;
  transition: $transition-border;
  margin-right: 0.5rem;
  outline: none;

  &.primary {
    background-color: $primary-color;
    color: white;

    &:hover {
      background-color: $primary-hover;
    }

    &:active {
      transform: translateY(1px);
    }
  }

  &.secondary {
    background-color: transparent;
    color: $primary-color;
    border: 1px solid $primary-color;

    &:hover {
      background-color: $primary-color;
      color: white;
    }

    @media (prefers-color-scheme: dark) {
      color: $tauri-color;
      border-color: $tauri-color;

      &:hover {
        background-color: $tauri-color;
        color: $bg-dark;
      }
    }
  }
}
