.container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.simpleHeader {
  position: relative !important; // Override fixed positioning for this example
  margin-bottom: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  
  h3 {
    margin: 0;
    font-size: 18px;
  }
}

.actionButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}

.layout {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  height: 200px;
}

.sidebar {
  position: relative !important; // Override fixed positioning for this example
  width: 250px;
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  border-radius: 8px;
  height: 100% !important;
}

.sidebarContent {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  
  p {
    margin: 0;
    font-weight: 500;
  }
}

.input {
  padding: 8px;
  border: none;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  
  &::placeholder {
    color: #666;
  }
}

.mainContent {
  flex: 1;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  font-style: italic;
}

.toolbar {
  position: relative !important; // Override fixed positioning for this example
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  border-radius: 8px;
}

.toolbarContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 100%;
  
  span {
    font-weight: 500;
  }
}

.toolbarButtons {
  display: flex;
  gap: 8px;
}

.toolButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.3);
  }
}