import React from 'react';
import { getCurrentWindow } from '@tauri-apps/api/window';
import DraggableArea from './DraggableArea';
import styles from './CustomTitleBar.module.scss';

interface CustomTitleBarProps {
  title?: string;
  showWindowControls?: boolean;
}

const CustomTitleBar: React.FC<CustomTitleBarProps> = ({ 
  title = 'Tauri App',
  showWindowControls = true 
}) => {
  const handleMinimize = async () => {
    try {
      const appWindow = getCurrentWindow();
      await appWindow.minimize();
    } catch (error) {
      console.error('Failed to minimize window:', error);
    }
  };

  const handleMaximize = async () => {
    try {
      const appWindow = getCurrentWindow();
      const isMaximized = await appWindow.isMaximized();
      if (isMaximized) {
        await appWindow.unmaximize();
      } else {
        await appWindow.maximize();
      }
    } catch (error) {
      console.error('Failed to toggle maximize window:', error);
    }
  };

  const handleClose = async () => {
    try {
      const appWindow = getCurrentWindow();
      await appWindow.close();
    } catch (error) {
      console.error('Failed to close window:', error);
    }
  };

  return (
    <DraggableArea className={styles.titleBar} height="40px">
      <div className={styles.titleContainer}>
        <h1 className={styles.title}>{title}</h1>
      </div>
      
      {showWindowControls && (
        <div className={styles.windowControls}>
          <button 
            className={`${styles.controlButton} ${styles.minimize}`}
            onClick={handleMinimize}
            aria-label="Minimize"
          >
            −
          </button>
          <button 
            className={`${styles.controlButton} ${styles.maximize}`}
            onClick={handleMaximize}
            aria-label="Maximize"
          >
            □
          </button>
          <button 
            className={`${styles.controlButton} ${styles.close}`}
            onClick={handleClose}
            aria-label="Close"
          >
            ×
          </button>
        </div>
      )}
    </DraggableArea>
  );
};

export default CustomTitleBar;