import React from 'react';
import DraggableArea from './DraggableArea';
import styles from './DraggableAreaExample.module.scss';

const DraggableAreaExample: React.FC = () => {
  return (
    <div className={styles.container}>
      <h2>Draggable Area Examples</h2>
      
      {/* Example 1: Simple draggable header */}
      <DraggableArea className={styles.simpleHeader} height="60px">
        <div className={styles.headerContent}>
          <h3>Drag this header to move the window</h3>
          <button className={styles.actionButton}>
            This button won't trigger drag
          </button>
        </div>
      </DraggableArea>

      {/* Example 2: Draggable sidebar */}
      <div className={styles.layout}>
        <DraggableArea className={styles.sidebar} height="200px">
          <div className={styles.sidebarContent}>
            <p>Draggable Sidebar</p>
            <p>Click and drag anywhere in this area</p>
            <input 
              type="text" 
              placeholder="Input won't trigger drag"
              className={styles.input}
            />
          </div>
        </DraggableArea>
        
        <div className={styles.mainContent}>
          <p>This is the main content area. It's not draggable.</p>
        </div>
      </div>

      {/* Example 3: Custom draggable toolbar */}
      <DraggableArea className={styles.toolbar} height="50px">
        <div className={styles.toolbarContent}>
          <span>Custom Toolbar</span>
          <div className={styles.toolbarButtons}>
            <button className={styles.toolButton}>Tool 1</button>
            <button className={styles.toolButton}>Tool 2</button>
            <button className={styles.toolButton}>Tool 3</button>
          </div>
        </div>
      </DraggableArea>
    </div>
  );
};

export default DraggableAreaExample;