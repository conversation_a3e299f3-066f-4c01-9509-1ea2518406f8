.draggableArea {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  user-select: none;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }

  // Ensure the area doesn't interfere with macOS traffic lights
  padding-left: 80px; // Space for traffic lights on macOS
  
  // On Windows/Linux, you might want different padding
  @media (not (os: macos)) {
    padding-left: 20px;
  }
}

// Style for any content within the draggable area
.draggableArea > * {
  pointer-events: auto; // Allow interaction with child elements
}

// Prevent dragging on interactive elements
.draggableArea button,
.draggableArea input,
.draggableArea select,
.draggableArea textarea,
.draggableArea [role="button"] {
  cursor: default;
  
  &:hover {
    cursor: pointer;
  }
}