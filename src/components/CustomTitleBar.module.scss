.titleBar {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.titleContainer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: auto;
}

.title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin: 0;
  text-align: center;
  
  // Dark mode support
  @media (prefers-color-scheme: dark) {
    color: #fff;
  }
}

.windowControls {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.controlButton {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: #666;
  font-size: 16px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
  }
  
  &:active {
    transform: scale(0.95);
  }
  
  // Dark mode support
  @media (prefers-color-scheme: dark) {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
    
    &:hover {
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.minimize {
  &:hover {
    background: rgba(255, 193, 7, 0.2);
    color: #ff9500;
  }
}

.maximize {
  &:hover {
    background: rgba(40, 167, 69, 0.2);
    color: #28a745;
  }
}

.close {
  &:hover {
    background: rgba(220, 53, 69, 0.2);
    color: #dc3545;
  }
}

// Hide window controls on macOS since it has native traffic lights
@media (os: macos) {
  .windowControls {
    display: none;
  }
}