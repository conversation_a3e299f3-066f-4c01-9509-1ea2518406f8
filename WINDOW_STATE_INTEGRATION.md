# Tauri Desktop Plugins Integration

This document describes the integration of Tauri desktop plugins: window-state and single-instance.

## Changes Made

### 1. Backend (Rust) Changes

#### Cargo.toml
- Added desktop-only plugin dependencies:
  ```toml
  [target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
  tauri-plugin-window-state = "2"
  tauri-plugin-single-instance = "2"
  ```

#### src/lib.rs
- Added `use tauri::Manager;` import for plugin functionality
- Added both plugins conditionally for desktop platforms only:
  ```rust
  #[cfg(desktop)]
  {
      builder = builder
          .plugin(tauri_plugin_window_state::Builder::new().build())
          .plugin(tauri_plugin_single_instance::init(|app, _args, _cwd| {
              // When a second instance is launched, focus the existing window
              let windows = app.webview_windows();
              if let Some(window) = windows.values().next() {
                  let _ = window.show();
                  let _ = window.set_focus();
                  let _ = window.unminimize();
              }
          }));
  }
  ```

### 2. Frontend (TypeScript/React) Changes

#### package.json
- Added `@tauri-apps/plugin-window-state` dependency

#### src/App.tsx
- Imported window state functions:
  ```typescript
  import { restoreStateCurrent, saveWindowState } from "@tauri-apps/plugin-window-state";
  ```
- Modified `setupAppWindow()` function to:
  1. Restore window state before showing the window
  2. Set up a close event listener to save window state before closing

### 3. Configuration Changes

#### tauri.conf.json
- No additional configuration needed for the window-state plugin in Tauri v2
- The plugin uses default settings and stores state in the app's data directory

## How It Works

### Window State Plugin
1. **Window Restoration**: When the app starts, `restoreStateCurrent()` is called before showing the window, which restores the previous window position, size, and state from the saved JSON file.

2. **Window State Saving**: When the user attempts to close the window, the `onCloseRequested` event listener triggers `saveWindowState()` to save the current window state to a JSON file.

3. **State Storage**: The window state is stored in a `window-state.json` file in the app's data directory.

### Single Instance Plugin
1. **Instance Detection**: When a user tries to launch a second instance of the application, the plugin detects this and prevents the new instance from starting.

2. **Window Focus**: Instead of starting a new instance, the plugin focuses the existing window by:
   - Showing the window if it's hidden
   - Bringing it to the front with `set_focus()`
   - Unminimizing it if it's minimized

3. **Cross-Platform**: Works on Windows, macOS, and Linux desktop platforms.

## Features Implemented

### Window State Plugin
- ✅ Save window position (x, y coordinates)
- ✅ Save window size (width, height)
- ✅ Save window state (maximized, minimized, etc.)
- ✅ Restore all saved properties on app startup
- ✅ Automatic state persistence on window close

### Single Instance Plugin
- ✅ Prevent multiple instances of the application
- ✅ Focus existing window when second instance is attempted
- ✅ Show and unminimize existing window automatically
- ✅ Cross-platform support (Windows, macOS, Linux)

### General
- ✅ **Desktop-only functionality** - both plugins are excluded from mobile builds (Android/iOS)

## Usage

The integration is automatic - no user interaction required. The window will:
1. Remember its last position and size when closed
2. Restore to the same position and size when reopened
3. Maintain maximized/minimized state across sessions

## Testing

### Window State Plugin
1. Run the app with `pnpm tauri dev` (requires Node.js 20.19+ or 22.12+)
2. Move and resize the window
3. Close the app
4. Reopen the app - it should restore to the previous position and size
5. Check for `window-state.json` file in the app's data directory

### Single Instance Plugin
1. Run the app with `pnpm tauri dev`
2. While the app is running, try to launch it again from the command line or desktop
3. The second instance should not start, and the existing window should come to the front
4. Try minimizing the window and launching again - it should unminimize and focus

## Files Modified

- `src-tauri/Cargo.toml` - Added window-state plugin dependency
- `src-tauri/src/lib.rs` - Integrated plugin in Tauri builder
- `src-tauri/tauri.conf.json` - Added plugin configuration
- `package.json` - Added frontend plugin dependency
- `src/App.tsx` - Implemented window state save/restore logic