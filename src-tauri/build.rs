use std::{env, fs, path::Path};

fn main() {
    let out_dir = env::var("OUT_DIR").unwrap();
    let dest_path = Path::new(&out_dir).join("i18n_rs.rs");

    let i18n_code = plugin_build::load_localization_files().unwrap();

    let generated_code = format!(
        r#"
pub static LANG_MAP: std::sync::LazyLock<std::collections::HashMap<&'static str, &'static str>> = std::sync::LazyLock::new(|| {{
    {}
}});
    "#,
        i18n_code
    );
    fs::write(dest_path, generated_code).unwrap();

    plugin_build::listen_localization_files();

    tauri_build::build()
}
