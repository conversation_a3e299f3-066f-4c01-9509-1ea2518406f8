use app_macros::manager;
use log;
use plugin_interface::LogAPI;

#[manager]
#[derive(Default)]
pub struct LogManager {}

impl LogAPI for LogManager {
    fn log(&self, level: plugin_interface::LogLevel, message: &str) {
        log::log!(level.to_level_filter(), "{}", message);
    }

    fn error(&self, message: &str) {
        log::error!("{}", message);
    }

    fn from_error(&self, error: &dyn std::any::Any) {
        if let Some(err) = error.downcast_ref::<dyn std::error::Error>() {
            log::error!("{}", err);
        } else {
            log::error!("Unknown error type");
        }
    }

    fn warn(&self, message: &str) {
        log::warn!("{}", message);
    }

    fn info(&self, message: &str) {
        log::info!("{}", message);
    }

    fn debug(&self, message: &str) {
        log::debug!("{}", message);
    }

    fn trace(&self, message: &str) {
        log::trace!("{}", message);
    }
}
