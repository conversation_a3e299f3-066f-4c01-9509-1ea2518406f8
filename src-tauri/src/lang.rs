use std::collections::HashMap;

use app_macros::manager;
use json_patch::merge;
use serde_json::Value;

#[manager]
#[derive(Default)]
pub struct LangManager {
    pub lang_map: HashMap<&'static str, Value>,
}

impl LangManager {
    pub fn add_raw(
        &mut self,
        map: HashMap<&'static str, &'static str>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        for (key, value) in map {
            let json_value: Value = serde_json::from_str(value)?;

            if self.lang_map.contains_key(key) {
                let mut target_value = self.lang_map.get_mut(key).unwrap();
                merge(&mut target_value, &json_value);
            } else {
                self.lang_map.insert(key, json_value);
            }
        }

        println!("lang_map: {:?}", self.lang_map);

        Ok(())
    }

    pub fn get_lang(key: Option<&str>) -> Option<Value> {
        LangManager::instance_mut(|m| {
            if let Some(key) = key {
                m.lang_map.get(key).cloned()
            } else {
                m.lang_map.get("en").cloned()
            }
        })
    }
}
