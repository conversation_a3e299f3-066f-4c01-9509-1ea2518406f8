use crate::{command::Command<PERSON>anager, lang::<PERSON><PERSON><PERSON><PERSON>, CORE_CONTEXT};
use plugin_interface::{CommandCallMeta, CommandMeta};
use serde_json::Value;

#[tauri::command]
pub fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

#[tauri::command]
pub fn get_lang(code: Option<&str>) -> Option<Value> {
    LangManager::get_lang(code)
}

#[tauri::command]
pub fn test_xxx(_app_handle: tauri::AppHandle) -> String {
    // format!("foo: {}, bar: {}", foo, bar)
    "".to_string()
}

#[tauri::command]
pub async fn call_plugin(meta: CommandCallMeta) -> Option<Value> {
    let meta = CommandMeta::new(meta, CORE_CONTEXT.get_handle());
    let r = CommandManager::call_plugin(&meta).await;
    println!("{:#?}", r);
    r
}

// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
// #[tauri::command]
// fn greet(name: &str) -> String {
//     format!("Hello, {}! You've been greeted from Rust!", name)
// }

// #[tauri::command]
// async fn fit_window_to_screen(window: tauri::Window) -> Result<(), String> {
//     // Get the primary monitor's work area (excludes taskbar/dock)
//     let monitor = window.primary_monitor()
//         .map_err(|e| format!("Failed to get primary monitor: {}", e))?
//         .ok_or("No primary monitor found")?;

//     let work_area = monitor.work_area();

//     // Set window size and position to fit the available screen area
//     window.set_size(tauri::Size::Physical(tauri::PhysicalSize {
//         width: work_area.size.width,
//         height: work_area.size.height,
//     }))
//     .map_err(|e| format!("Failed to set window size: {}", e))?;

//     window.set_position(tauri::Position::Physical(tauri::PhysicalPosition {
//         x: work_area.position.x,
//         y: work_area.position.y,
//     }))
//     .map_err(|e| format!("Failed to set window position: {}", e))?;

//     Ok(())
// }
