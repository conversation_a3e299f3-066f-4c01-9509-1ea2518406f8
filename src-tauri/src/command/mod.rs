use app_macros::manager;
use plugin_interface::{CallbackCommand, CommandMeta};
use serde_json::Value;
use std::collections::HashMap;
use std::future::ready;

pub mod internal;

#[manager]
#[derive(Default)]
pub struct CommandManager {
    commands: HashMap<String, CallbackCommand>,
}

impl CommandManager {
    pub fn register_commands(&mut self, plugin_id: &str, command: CallbackCommand) {
        self.commands.insert(plugin_id.to_string(), command);
    }

    pub async fn call_plugin(meta: &CommandMeta) -> Option<Value> {
        let cmd = {
            let command_manager = CommandManager::instance().lock().unwrap();
            command_manager.commands.get(&meta.id).cloned()
        };

        if let Some(cmd) = cmd {
            let func = cmd.as_ref();
            // let mut func = cmd.lock().await;
            return func(meta).await;
        }

        Box::pin(ready(None)).await
    }
}
