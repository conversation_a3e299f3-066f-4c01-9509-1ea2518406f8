use std::sync::{Arc, Mutex};

use plugin_interface::{CallbackCommand, ContextHandle, CoreAPI, LogAPI};

use crate::{
    command::CommandManager, lang::LangManager, log::LogManager, plugin::PluginManager,
    CORE_CONTEXT,
};

// Core API struct to expose main app functionality
// #[derive(Clone)]
pub struct CoreContext {
    app_state: Arc<Mutex<AppState>>,
    pub lang_manager: &'static Mutex<LangManager>,
    pub plugin_manager: &'static Mutex<PluginManager>,
    pub log_manager: &'static Mutex<LogManager>, // Add other shared resources here
}

impl CoreContext {
    pub fn get_handle(&self) -> ContextHandle {
        // 获取 Arc 的引用
        let context_ref = &*CORE_CONTEXT;
        // 然后可以克隆 Arc（如果需要所有权）
        let context_clone = Arc::clone(&context_ref);
        ContextHandle(context_clone)
    }
}

impl CoreContext {
    pub fn new() -> Arc<Self> {
        let inst = Self {
            lang_manager: LangManager::instance(),
            plugin_manager: PluginManager::instance(),
            log_manager: LogManager::instance(),
            app_state: Arc::new(Mutex::new(AppState::default())),
        };

        // // init lang
        // let mut lang = inst.lang_manager.lock().unwrap();
        // lang.add_raw(LANG_MAP.clone()).unwrap();

        // // init plugin
        // let mut plugin = inst.plugin_manager.lock().unwrap();
        // plugin.init(&CORE_CONTEXT);
        // plugin.scan_and_load(Path::new("./plugins"));

        Arc::new(inst)
    }
}

impl CoreAPI for CoreContext {
    // Example core method
    fn get_data(&self) -> String {
        let state = self.app_state.lock().unwrap();
        state.data.clone()
    }

    fn register_commands(&self, plugin_id: &str, command: CallbackCommand) {
        CommandManager::instance_mut(|m| {
            m.register_commands(plugin_id, command);
        })
    }

    fn api_log(&self) -> &'static Mutex<dyn LogAPI> {
        self.log_manager
    }
    // fn api_loga(&self) ->  &'static Mutex<dyn LogAPI> {
    //     self.log_manager.lock().unwrap()
    // }

    // fn register_commands(&self, commands: &mut std::collections::HashMap<String, Box<dyn plugin_interface::Command>>) {
    //     CommandManager::instance_mut(|m| {
    //         m.register_commands(commands);
    //     })
    // }
}

// App state example
#[derive(Default)]
pub struct AppState {
    pub data: String,
    // Add other application state fields
}
