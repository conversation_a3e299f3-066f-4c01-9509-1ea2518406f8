use app_macros::manager;
use libloading::{Library, Symbol};
use plugin_interface::{Plugin, PluginBox};
use std::collections::HashMap;
use std::panic;
use std::path::{Path, PathBuf};

use crate::core::CoreContext;
use crate::lang::LangManager;

struct LoadedPlugin {
    library: Library,
    instance: Box<dyn Plugin>,
}

// Plugin manager to handle plugins
#[manager]
#[derive(Default)]
pub struct PluginManager {
    plugins: HashMap<String, LoadedPlugin>,
    core_api: Option<&'static CoreContext>,
}

impl PluginManager {
    pub fn init(&mut self, core_api: &'static CoreContext) {
        self.plugins = HashMap::new();
        self.core_api = Some(core_api);
    }

    pub fn load_plugin(&mut self, path: PathBuf) -> Result<(), Box<dyn std::error::Error>> {
        // Wrap the entire plugin loading process in panic recovery
        let result = panic::catch_unwind(panic::AssertUnwindSafe(
            || -> Result<(), Box<dyn std::error::Error>> {
                unsafe {
                    let lib = Library::new(&path)?;
                    let constructor: Symbol<fn() -> *mut PluginBox> = lib.get(b"_create_plugin")?;

                    // Wrap the constructor call in another panic catch since it's the most likely to panic
                    let boxed_raw = panic::catch_unwind(panic::AssertUnwindSafe(|| constructor()))
                        .map_err(|_| "Plugin constructor panicked")?;

                    let box_instance = Box::from_raw(boxed_raw);
                    let instance = Box::from_raw(box_instance.ptr);

                    // Wrap plugin method calls that could panic
                    let plugin_name = panic::catch_unwind(panic::AssertUnwindSafe(|| {
                        instance.name().to_string()
                    }))
                    .map_err(|_| "Plugin name() method panicked")?;

                    let manifest = instance.get_manifest();

                    // println!("manifest: {:?}", manifest.icon);
                    // println!("manifest: {:?}", manifest.i18n);

                    LangManager::instance_mut(|m| {
                        m.add_raw(manifest.i18n).unwrap();
                    });

                    // Wrap on_load call which could also panic
                    let core_api = self.core_api.unwrap();
                    panic::catch_unwind(panic::AssertUnwindSafe(|| instance.on_load(core_api)))
                        .map_err(|_| "Plugin on_load() method panicked")?;

                    self.plugins.insert(
                        plugin_name,
                        LoadedPlugin {
                            library: lib,
                            instance,
                        },
                    );

                    Ok(())
                }
            },
        ));

        match result {
            Ok(inner_result) => inner_result,
            Err(_) => Err("Plugin loading panicked during execution".into()),
        }
    }

    pub fn unload_plugin(&mut self, name: &str) {
        if let Some(plugin) = self.plugins.remove(name) {
            // Wrap the on_unload call in panic recovery
            let _ = panic::catch_unwind(panic::AssertUnwindSafe(|| {
                plugin.instance.on_unload();
            }))
            .map_err(|_| {
                eprintln!("Plugin '{}' panicked during unload", name);
            });
            // Library is dropped here and unloaded
        }
    }

    pub fn get_plugin(&self, name: &str) -> Option<&dyn Plugin> {
        self.plugins.get(name).map(|p| p.instance.as_ref())
    }

    pub fn scan_and_load(&mut self, plugin_dir: &Path) {
        if let Ok(entries) = std::fs::read_dir(plugin_dir) {
            for entry in entries.filter_map(Result::ok) {
                let path = entry.path();
                if is_valid_plugin_file(&path) {
                    if let Err(e) = self.load_plugin(path) {
                        eprintln!("Failed to load plugin: {}", e);
                    }
                }
            }
        }
    }
}

fn is_valid_plugin_file(path: &Path) -> bool {
    let extension = path.extension().and_then(|s| s.to_str());
    #[cfg(target_os = "windows")]
    return extension == Some("dll");
    #[cfg(target_os = "macos")]
    return extension == Some("dylib");
    #[cfg(target_os = "linux")]
    return extension == Some("so");
}
