mod command;
mod core;
mod lang;
mod log;
mod plugin;

use tauri::Manager;

use crate::core::CoreContext;
use crate::lang::LangManager;
use crate::plugin::PluginManager;
use command::internal::*;
use ffmpeg_next::format;
use std::env;
use std::path::PathBuf;
use std::sync::LazyLock;
use std::{
    path::Path,
    sync::Arc,
};

pub static CORE_CONTEXT: LazyLock<Arc<CoreContext>> = LazyLock::new(|| CoreContext::new());
// static LANG_MAP: std::sync::LazyLock<std::collections::HashMap<&'static str, &'static str>> = std::sync::LazyLock::new(|| {
//     std::collections::HashMap::new()
// });
include!(concat!(env!("OUT_DIR"), "/i18n_rs.rs"));

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    LangManager::instance_mut(|m| {
        m.add_raw(LANG_MAP.clone()).unwrap();
    });

    PluginManager::instance_mut(|m| {
        // m.init(app_state);
        m.init(&CORE_CONTEXT);
    });

    let dir = Path::new(".");
    let dd = dir.canonicalize();
    println!("{:?}", dd);

    PluginManager::instance_mut(|m| {
        m.scan_and_load(Path::new("./plugins"));
    });

    let mut builder = tauri::Builder::default()
        .plugin(tauri_plugin_log::Builder::new().build())
        .plugin(tauri_plugin_opener::init());

    // Only add desktop-specific plugins on desktop platforms
    #[cfg(desktop)]
    {
        builder = builder
            // .plugin(tauri_plugin_window_state::Builder::new().build())
            .plugin(tauri_plugin_single_instance::init(|app, _args, _cwd| {
                // When a second instance is launched, focus the existing window
                let windows = tauri::Manager::webview_windows(app);
                if let Some(window) = windows.values().next() {
                    let _ = window.show();
                    let _ = window.set_focus();
                    let _ = window.unminimize();
                }
            }));
    }

    builder
        .setup(|app| {
            set_ffmpeg_library_path(app);
            // Get the primary monitor's work area (excludes taskbar/dock)
            let monitor = app
                .primary_monitor()?
                .ok_or_else(|| "No primary monitor found")?;
            let work_area = monitor.work_area();

            // Create window with available screen size
            let window = tauri::WebviewWindowBuilder::from_config(
                app.handle(),
                &app.config().app.windows[0],
            )?
            .inner_size(work_area.size.width as f64, work_area.size.height as f64)
            .position(work_area.position.x as f64, work_area.position.y as f64)
            .maximizable(true)
            .resizable(true)
            .build()?;

            #[cfg(debug_assertions)]
            window.open_devtools();

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            greet,
            get_lang,
            test_xxx,
            call_plugin,
        ])
        // .invoke_handler(move |__tauri_invoke__| {
        //     let __tauri_cmd__ = __tauri_invoke__.message.command();
        //     match __tauri_cmd__ {
        //         "greet" => {
        //             ({
        //                 move || {
        //                     #[allow(unused_imports)]
        //                     use ::tauri::ipc::private::*;
        //                     #[allow(unused_variables)]
        //                     let ::tauri::ipc::Invoke {
        //                         message: __tauri_message__,
        //                         resolver: __tauri_resolver__,
        //                         acl: __tauri_acl__,
        //                     } = __tauri_invoke__;
        //                     let result = greet(
        //                         match ::tauri::ipc::CommandArg::from_command(::tauri::ipc::CommandItem {
        //                             plugin: ::core::option::Option::None,
        //                             name: "greet",
        //                             key: "name",
        //                             message: &__tauri_message__,
        //                             acl: &__tauri_acl__,
        //                         }) {
        //                             Ok(arg) => arg,
        //                             Err(err) => {
        //                                 __tauri_resolver__.invoke_error(err);
        //                                 return true;
        //                             }
        //                         },
        //                     );
        //                     let kind = (&result).blocking_kind();
        //                     kind.block(result, __tauri_resolver__);
        //                     return true;
        //                 }
        //             })()
        //         }
        //         "get_lang" => {
        //         ({
        //             move || {
        //                 #[allow(unused_imports)]
        //                 use ::tauri::ipc::private::*;
        //                 #[allow(unused_variables)]
        //                 let ::tauri::ipc::Invoke {
        //                     message: __tauri_message__,
        //                     resolver: __tauri_resolver__,
        //                     acl: __tauri_acl__,
        //                 } = __tauri_invoke__;
        //                 let result = get_lang(
        //                     match ::tauri::ipc::CommandArg::from_command(::tauri::ipc::CommandItem {
        //                         plugin: ::core::option::Option::None,
        //                         name: "get_lang",
        //                         key: "code",
        //                         message: &__tauri_message__,
        //                         acl: &__tauri_acl__,
        //                     }) {
        //                         Ok(arg) => arg,
        //                         Err(err) => {
        //                             __tauri_resolver__.invoke_error(err);
        //                             return true;
        //                         }
        //                     },
        //                 );
        //                 let kind = (&result).blocking_kind();
        //                 kind.block(result, __tauri_resolver__);
        //                 return true;
        //             }
        //         })()
        //         }
        //             "test_xxx" => {
        //             ({
        //                 move || {
        //                     #[allow(unused_imports)]
        //                     use ::tauri::ipc::private::*;
        //                     #[allow(unused_variables)]
        //                     let ::tauri::ipc::Invoke {
        //                         message: __tauri_message__,
        //                         resolver: __tauri_resolver__,
        //                         acl: __tauri_acl__,
        //                     } = __tauri_invoke__;
        //                     let result = test_xxx(
        //                         match ::tauri::ipc::CommandArg::from_command(::tauri::ipc::CommandItem {
        //                             plugin: ::core::option::Option::None,
        //                             name: "test_xxx",
        //                             key: "appHandle",
        //                             message: &__tauri_message__,
        //                             acl: &__tauri_acl__,
        //                         }) {
        //                             Ok(arg) => arg,
        //                             Err(err) => {
        //                                 __tauri_resolver__.invoke_error(err);
        //                                 return true;
        //                             }
        //                         },
        //                     );
        //                     let kind = (&result).blocking_kind();
        //                     kind.block(result, __tauri_resolver__);
        //                     return true;
        //                 }
        //             })()
        //         }
        //         _ => true
        //     }
        // })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

fn set_ffmpeg_library_path(app: &tauri::App) {
    let resource_dir = app.path().resource_dir().unwrap();
    println!("resource_dir: {:?}", resource_dir);

    // 将 FFmpeg 路径添加到动态库搜索路径
    let current_path = env::var("PATH").unwrap_or_default();
    let new_path = if cfg!(windows) {
        format!("{};{}", resource_dir.display(), current_path)
    } else {
        format!("{}:{}", resource_dir.display(), current_path)
    };

    env::set_var("PATH", &new_path);

    // 对于 Linux/macOS，还需要设置 LD_LIBRARY_PATH/DYLD_LIBRARY_PATH
    #[cfg(target_os = "linux")]
    env::set_var(
        "LD_LIBRARY_PATH",
        format!(
            "{}:{}",
            resource_dir.display(),
            env::var("LD_LIBRARY_PATH").unwrap_or_default()
        ),
    );

    #[cfg(target_os = "macos")]
    env::set_var(
        "DYLD_LIBRARY_PATH",
        format!(
            "{}:{}",
            resource_dir.display(),
            env::var("DYLD_LIBRARY_PATH").unwrap_or_default()
        ),
    );

    let input = resource_dir.join("input.mp4");
    check_ffmpeg_availability(&input).unwrap();
}

fn check_ffmpeg_availability(input: &PathBuf) -> Result<(), String> {
    match format::input(input) {
        Ok(_) => Ok(()),
        Err(e) => {
            eprintln!("input not found: {}", e);
            Ok(())
        }
    }
}
