[package]
name = "tauri-app"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "tauri_app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }
plugin_build = { path = "./crates/plugin_build" }

[dependencies]
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
libloading = "0.8.8"
plugin_interface = { path = "./crates/plugin_interface" }
app_macros = { path = "./crates/app_macros" }
emitter = { path = "./crates/emitter" }
json-patch = "4.0.0"
ffmpeg-next = "8.0.0"
tokio = "1.47.1"
tauri-plugin-log = "2"
log = "0.4.28"

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-window-state = "2"
tauri-plugin-single-instance = "2"

