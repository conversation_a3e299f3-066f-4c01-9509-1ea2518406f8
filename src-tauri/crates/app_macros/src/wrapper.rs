
use proc_macro::TokenStream;
use proc_macro2::TokenStream as TokenStream2;
use quote::quote;
use syn::{
    FnArg, ItemFn, Pat, Visibility,
    ext::IdentExt,
    parse_macro_input,
    spanned::Spanned,
};

use crate::command::{format_command_wrapper, path_to_command};

pub fn wrapper(_attributes: TokenStream, item: TokenStream) -> TokenStream {
    let function = parse_macro_input!(item as ItemFn);
    let wrapper = format_command_wrapper(&function.sig.ident);
    let visibility = &function.vis;

    let is_async = function.sig.asyncness.is_some();

    let maybe_macro_export = match &function.vis {
        Visibility::Public(_) | Visibility::Restricted(_) => quote!(#[macro_export]),
        _ => TokenStream2::default(),
    };

    let await_identifier = if is_async { quote!(.await) } else { quote!() };

    let match_body = quote!({
      Ok(arg) => arg,
      Err(err) => {
        let log = $meta.context.0.api_log().lock().unwrap();
        log.from_error(&err);
        return None;
      }
    });

    let args: Vec<TokenStream2> = parse_args(&function).unwrap();

    let r = quote!(
      #function

      #maybe_macro_export
      #[doc(hidden)]
      macro_rules! #wrapper {
        ($path:path, $meta:ident) => {
          {

            let result = $path( #(match #args #match_body),* )#await_identifier;
            if let Ok(v) = serde_json::to_value(result) {
              return Some(v);
            }
            None
          }
        };
      }

      #[allow(unused_imports)]
      #visibility use #wrapper;
    );

    // let rs = r.to_string();
    // println!("{rs}");

    r.into()
}

fn parse_args(function: &ItemFn) -> syn::Result<Vec<TokenStream2>> {
    function
        .sig
        .inputs
        .iter()
        .map(|arg| parse_arg(arg))
        .collect()
}

fn parse_arg(arg: &FnArg) -> syn::Result<TokenStream2> {
    let mut arg = match arg {
        FnArg::Typed(arg) => arg.pat.as_ref().clone(),
        FnArg::Receiver(arg) => {
            return Err(syn::Error::new(
                arg.span(),
                "unable to use self as a command function parameter",
            ));
        }
    };
    let key = match &mut arg {
        Pat::Ident(arg) => arg.ident.unraw().to_string(),
        Pat::Wild(_) => "".into(), // we always convert to camelCase, so "_" will end up empty anyways
        Pat::Struct(s) => path_to_command(&mut s.path).ident.to_string(),
        Pat::TupleStruct(s) => path_to_command(&mut s.path).ident.to_string(),
        err => {
            return Err(syn::Error::new(
                err.span(),
                "only named, wildcard, struct, and tuple struct arguments allowed",
            ));
        }
    };

    Ok(quote!(
      plugin_interface::command::CommandArg::from_command(plugin_interface::command::CommandCallMeta {
        payload: &$meta.payload,
        key: #key,
        context: $meta.context.clone(),
      })
    ))
}
