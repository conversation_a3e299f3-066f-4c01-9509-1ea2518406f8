use quote::format_ident;
use syn::{
    Attribute, Ident, Path, PathSegment, Token,
    parse::{<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ParseStream},
};

pub fn path_to_command(path: &mut Path) -> &mut PathSegment {
    path.segments
        .last_mut()
        .expect("parsed syn::Path has no segment")
}
/// The autogenerated wrapper ident.
pub fn format_command_wrapper(function: &Ident) -> Ident {
    quote::format_ident!("__pcmd__{}", function)
}

struct CommandDef {
    path: Path,
    attrs: Vec<Attribute>,
}

impl Parse for CommandDef {
    fn parse(input: ParseStream) -> syn::Result<Self> {
        let attrs = input.call(Attribute::parse_outer)?;
        let path = input.parse()?;
        // let path = input.parse()?;

        Ok(CommandDef { path, attrs })
    }
}

pub struct Handler {
    command_defs: Vec<CommandDef>,
    commands: Vec<Ident>,
    wrappers: Vec<Path>,
}

impl Parse for Handler {
    fn parse(input: &ParseBuffer<'_>) -> syn::Result<Self> {
        let command_defs: Vec<CommandDef> = input
            .parse_terminated(CommandDef::parse, Token![,])?
            .into_iter()
            .collect();

        let mut commands = Vec::new();
        let mut wrappers = Vec::new();

        // let ref_command_def: &Vec<CommandDef> = &command_defs;

        // parse the command names and wrappers from the passed paths
        for command_def in &command_defs {
            let mut wrapper = command_def.path.clone();
            let last = path_to_command(&mut wrapper);

            // the name of the actual command function
            let command = last.ident.clone();

            // set the path to the command function wrapper
            last.ident = format_command_wrapper(&command);

            commands.push(command);
            wrappers.push(wrapper);
        }

        Ok(Self {
            command_defs,
            commands,
            wrappers,
        })
    }
}

impl From<Handler> for proc_macro::TokenStream {
    fn from(
        Handler {
            command_defs,
            commands,
            wrappers,
        }: Handler,
    ) -> Self {
        let meta = format_ident!("__plugin_meta__");

        let (paths, attrs): (Vec<Path>, Vec<Vec<Attribute>>) = command_defs
            .into_iter()
            .map(|def| (def.path, def.attrs))
            .unzip();

        let r = quote::quote! {
          std::sync::Arc::new(|#meta| {
            Box::pin(async move {
              match #meta.method.as_str() {
                #(#(#attrs)* stringify!(#commands) => #wrappers!(#paths, #meta) ,)*
                _ => None
              }
            })
          })
        };

        // let rs = r.to_string();
        // println!("{rs}");

        r.into()
    }
}
