mod command;
mod wrapper;

use command::<PERSON><PERSON>;
use proc_macro::TokenStream;
use quote::quote;
use syn::{Data, DataStruct, DeriveInput, Fields, FieldsNamed, parse_macro_input};

#[proc_macro_attribute]
pub fn add_timestamp(_attr: TokenStream, item: TokenStream) -> TokenStream {
    eprintln!("item is: {item:?}");
    eprintln!("attr is: {_attr:?}");

    let input = parse_macro_input!(item as DeriveInput);
    // eprint!("ast is {input:?}");

    // let input = parse_macro_input!(item as DeriveInput);
    let name = &input.ident;

    let expanded = quote! {
        #input

        impl #name {
            pub fn get_timestamp() -> u64 {
                use std::time::{SystemTime, UNIX_EPOCH};
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .expect("Time went backwards")
                    .as_secs()
            }
        }
    };

    // TokenStream::from(expanded)
    expanded.into()
}

#[proc_macro_attribute]
pub fn manager(_args: TokenStream, input: TokenStream) -> TokenStream {
    let mut input = parse_macro_input!(input as DeriveInput);
    let name = &input.ident;

    let (impl_generics, ty_generics, where_clause) = input.generics.split_for_impl();

    // 修改结构体，添加 listeners 字段
    if let Data::Struct(DataStruct {
        fields: Fields::Named(FieldsNamed { named: fields, .. }),
        ..
    }) = &mut input.data
    {
        let mut new_fields = fields.clone();

        // 检查是否已经有 listeners 字段
        let has_listeners = new_fields.iter().any(|field| {
            field
                .ident
                .as_ref()
                .map(|ident| ident == "_emitter")
                .unwrap_or(false)
        });

        if !has_listeners {
            // 添加 listeners 字段
            new_fields.push(syn::parse_quote! {
                pub emitter: emitter::Emitter
            });

            // 更新结构体的字段
            if let Data::Struct(DataStruct {
                fields: Fields::Named(FieldsNamed { named, .. }),
                ..
            }) = &mut input.data
            {
                *named = new_fields;
            }
        }
    }

    let expanded = quote! {
        #input

        //  impl #impl_generics Emitter for #name #ty_generics #where_clause {

        impl #impl_generics #name #ty_generics #where_clause {
            pub fn instance() -> &'static std::sync::Mutex<#name> {
                static INSTANCE: std::sync::OnceLock<std::sync::Mutex<#name>> =
                    std::sync::OnceLock::new();
                INSTANCE.get_or_init(|| std::sync::Mutex::new(#name::default()))
            }

            // with the mutex, call fnonce
            pub fn instance_mut<F, R>(f: F) -> R
            where
                F: FnOnce(&mut #name) -> R,
            {
                let mut instance = Self::instance().lock().unwrap();
                f(&mut instance)
            }

            pub fn emit<T>(&mut self, event: &str, data: T)
            where
                T: 'static + Clone,
            {
                self.emitter.emit(event, data);
            }
            pub fn on<T, F>(&mut self, event: &str, callback: F) -> emitter::ListenerHandle
            where
                T: 'static,
                F: FnMut(T) + Send + 'static,
            {
                self.emitter.on(event, callback)
            }

        }
    };

    expanded.into()
}

#[proc_macro]
pub fn generate_plugin_handler(item: TokenStream) -> TokenStream {
    parse_macro_input!(item as Handler).into()
}

#[proc_macro_attribute]
pub fn command(attributes: TokenStream, item: TokenStream) -> TokenStream {
    wrapper::wrapper(attributes, item)
}
