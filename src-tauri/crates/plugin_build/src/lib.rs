use base64::Engine;
use glob::glob;
use serde_json::Value;
use std::{collections::HashMap, env, fs, mem, path::Path};

pub fn build() -> Result<(), Box<dyn std::error::Error>> {
    let manifest_path = Path::new("manifest.json");
    let out_dir = env::var("OUT_DIR").unwrap();
    let dest_path = Path::new(&out_dir).join("manifest_rs.rs");
    let manifest_content = fs::read_to_string(manifest_path).unwrap();
    let manifest: serde_json::Value = serde_json::from_str(&manifest_content).unwrap();

    let id = manifest["id"].as_str().unwrap_or_default();
    let display_name = manifest["display_name"].as_str().unwrap_or_default();
    let version = manifest["version"].as_str().unwrap_or_default();
    let description = manifest["description"].as_str().unwrap_or_default();

    let icon_path = manifest["icon"].as_str().unwrap_or_default();
    let icon_bytes = if !icon_path.is_empty() {
        match fs::read(icon_path) {
            Ok(bytes) => bytes,
            Err(e) => {
                eprintln!("Warning: Failed to read icon file '{}': {}", icon_path, e);
                Vec::new()
            }
        }
    } else {
        Vec::new()
    };

    let icon_base64 = if !icon_bytes.is_empty() {
        base64::engine::general_purpose::STANDARD.encode(&icon_bytes)
    } else {
        String::new()
    };

    let i18n = load_localization_files()?;

    let generated_code = format!(
        r#"
use std::sync::LazyLock;

pub static PLUGIN_MANIFEST: LazyLock<PluginManifest> = LazyLock::new(|| {{
    PluginManifest {{
        id: "{}",
        display_name: "{}",
        version: "{}",
        icon: "{}",
        description: "{}",
        i18n: {}
    }}
}});
    "#,
        id, display_name, version, icon_base64, description, i18n
    );

    fs::write(dest_path, generated_code).unwrap();

    println!("cargo:rerun-if-changed=manifest.json");
    // println!("cargo:rerun-if-changed=build.rs");
    println!("cargo:rerun-if-changed={}", icon_path); // 图标文件改变时也重新编译

    listen_localization_files();

    Ok(())
}

pub fn listen_localization_files() {
    // 匹配所有 messages.{lang}.json 文件
    for entry in glob("messages.*.json").expect("Failed to read glob pattern") {
        if let Ok(path) = entry {
            println!("cargo:rerun-if-changed={}", path.display());
        }
    }
}

pub fn load_localization_files() -> Result<String, Box<dyn std::error::Error>> {
    let mut translations = HashMap::new();
    let current_dir = Path::new(".");

    // 读取当前目录
    for entry in fs::read_dir(current_dir)? {
        let entry = entry?;
        let path = entry.path();

        // 检查文件名是否符合 messages.{language}.json 格式
        if let Some(file_name) = path.file_name().and_then(|n| n.to_str()) {
            if let Some(language) = parse_language_from_filename(file_name) {
                // 读取文件内容
                let content = fs::read_to_string(&path)?;

                let mut parsed_json = serde_json::from_str::<Value>(&content)?;
                add_prefix_to_json_values(&mut parsed_json);
                let content = serde_json::to_string(&parsed_json)?;
                let content = format!("{:#?}", content);

                // 将 String 转换为 &'static str
                let static_str: &'static str = Box::leak(language.into_boxed_str());
                let content_str: &'static str = Box::leak(content.into_boxed_str());
                translations.insert(static_str, content_str);
            }
        }
    }

    Ok(format!(
        r#"
std::collections::HashMap::from([
    {}
])"#,
        translations
            .iter()
            // .map(|(k, v)|) { (k, v) })
            .map(|(k, v)| format!(r#"("{}", {})"#, k, v))
            .collect::<Vec<_>>()
            .join(",")
    ))
}

fn parse_language_from_filename(filename: &str) -> Option<String> {
    if filename.starts_with("messages.") && filename.ends_with(".json") {
        let start = "messages.".len();
        let end = filename.len() - ".json".len();
        let language = &filename[start..end];

        // 简单的语言代码验证（可根据需要调整）
        if !language.is_empty() && language.chars().all(|c| c.is_ascii_lowercase() || c == '-') {
            return Some(language.to_string());
        }
    }
    None
}

fn add_prefix_to_json_values(json: &mut Value) {
    let prefix = env::var("CARGO_PKG_NAME").unwrap();

    if let Value::Object(obj) = json {
        // 获取原对象的所有权并替换为空map
        let old_map = mem::take(obj);

        // 遍历原对象的所有键值对
        for (key, value) in old_map {
            // 为每个键添加前缀
            let new_key = format!("{}.{}", prefix, key);
            obj.insert(new_key, value);
        }
    }
}

// fn add_prefix_to_keys(json: &mut Value, prefix: &str) {
//     if let Value::Object(obj) = json {
//         // 先收集所有的键
//         let keys: Vec<String> = obj.keys().cloned().collect();

//         // 遍历所有键，修改并重新插入
//         for key in keys {
//             if let Some(value) = obj.remove(&key) {
//                 let new_key = format!("{}{}", prefix, key);
//                 obj.insert(new_key, value);
//             }
//         }
//     }
// }
