
use plugin_interface::{CoreAPI, Plugin, PluginBox, PluginManifest};

use app_macros::{command, generate_plugin_handler};

include!(concat!(env!("OUT_DIR"), "/manifest_rs.rs"));

#[command]
async fn greet(name: Option<String>) -> String {
    let s = format!("Hello, {}!", name.unwrap());
    println!("{s}");
    s
}

struct ExamplePlugin;

impl Plugin for ExamplePlugin {
    fn name(&self) -> &str {
        PLUGIN_MANIFEST.id
    }

    fn get_manifest(&self) -> PluginManifest {
        PLUGIN_MANIFEST.clone()
    }

    fn on_load(&self, core: &dyn CoreAPI) {
        core.register_commands(PLUGIN_MANIFEST.id, generate_plugin_handler![greet]);

        println!("Core data: {}", core.get_data());
    }

    fn on_unload(&self) {
        println!("Example plugin unloaded");
    }
}

#[no_mangle]
pub extern "C" fn _create_plugin() -> *mut PluginBox {
    Box::into_raw(Box::new(PluginBox {
        ptr: Box::into_raw(Box::new(ExamplePlugin)),
    }))
}
