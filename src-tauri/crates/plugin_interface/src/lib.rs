pub mod command;

use std::{
    collections::HashMap,
    future::Future,
    pin::Pin,
    sync::{Arc, Mutex},
};

use serde::{Deserialize, Serialize};
use serde_json::Value;

pub trait Plugin: Send {
    fn name(&self) -> &str;
    fn get_manifest(&self) -> PluginManifest;
    fn on_load(&self, core: &dyn CoreAPI);
    fn on_unload(&self);
}

#[async_trait::async_trait]
pub trait Command: Send {
    async fn call(&mut self, meta: &CommandMeta) -> Option<Value>;
}

// pub type CallbackCommand = Box<dyn Fn(&CommandMeta) -> Pin<Box<dyn Future<Output = Option<Value>> + Send + '_>> + Send + Sync + 'static>;
pub type CallbackCommand = Arc<
    dyn Fn(&CommandMeta) -> Pin<Box<(dyn Future<Output = Option<Value>> + Send + '_)>>
        + Send
        + Sync
        + 'static,
>;

pub enum LogLevel {
    Error,
    Warn,
    Info,
    Debug,
    Trace,
}

pub trait LogAPI {
    fn log(&self, level: LogLevel, message: &str);
    fn error(&self, message: &str);
    fn from_error(&self, error: &dyn std::any::Any);
    fn warn(&self, message: &str);
    fn info(&self, message: &str);
    fn debug(&self, message: &str);
    fn trace(&self, message: &str);
}

#[async_trait::async_trait]
pub trait CoreAPI {
    fn api_log(&self) -> &'static Mutex<dyn LogAPI>;
    fn get_data(&self) -> String;

    // fn register_commands<F>(&self, func: F)
    // where F: AsyncFn(&CommandMeta) -> Option<Value> + Send + Sync + 'static;

    // fn register_commands(
    //     &self,
    //     func: Box<dyn Fn(&CommandMeta) -> Option<Value> + Send + Sync + 'static>
    // );
    fn register_commands(&self, plugin_id: &str, func: CallbackCommand);
}

type ContextHandleType = Arc<dyn CoreAPI + Send + Sync + 'static>;
pub struct ContextHandle(pub ContextHandleType);

impl Clone for ContextHandle {
    fn clone(&self) -> Self {
        // This requires that CoreAPI implements Clone
        ContextHandle(Arc::clone(&self.0))
    }
}

// #[repr(C)]
// pub struct PluginManifest {
//     pub id: *const u8,
//     pub display_name: *const u8,
//     pub version: *const u8,
//     pub icon: *const u8,
//     pub description: *const u8,
// }
#[repr(C)]
pub struct PluginBox {
    pub ptr: *mut dyn Plugin,
}

#[derive(Debug, Clone)]
pub struct PluginManifest {
    pub id: &'static str,
    pub display_name: &'static str,
    pub version: &'static str,
    pub icon: &'static str,
    pub description: &'static str,
    pub i18n: HashMap<&'static str, &'static str>,
}

pub struct CommandMeta {
    pub id: String,
    pub method: String,
    pub payload: Option<Value>,
    pub context: ContextHandle,
}

impl CommandMeta {
    pub fn new(m: CommandCallMeta, context: ContextHandle) -> Self {
        CommandMeta {
            id: m.id,
            method: m.method,
            payload: m.payload,
            context: context,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct CommandCallMeta {
    pub id: String,
    pub method: String,
    pub payload: Option<Value>,
}

// pub type PluginHandle = *mut ();

// // Required for cross-language FFI
// #[no_mangle]
// pub extern "C" fn _create_plugin() -> PluginHandle {
//     panic!("This should be implemented by the plugin");
// }
