
use serde::{
    de::{<PERSON><PERSON><PERSON>, Visitor},
    Deserialize, Deserializer,
};
use serde_json::Value;

use crate::ContextHandle;

pub struct CommandCallMeta<'a> {
    pub key: &'static str,
    pub payload: &'a Option<Value>,
    pub context: ContextHandle,
}

pub trait CommandArg<'de>: Sized {
    fn from_command(command: CommandCallMeta<'de>) -> Result<Self, serde_json::Error>;
}

impl<'de, D> CommandArg<'de> for D
where
    D: Deserialize<'de>,
{
    fn from_command(command: CommandCallMeta<'de>) -> Result<D, serde_json::Error> {
        Self::deserialize(command)
        // .map_err(|_e| ParseError(command.payload.unwrap_or(Value::Null)))
    }
}

impl<'de> CommandArg<'de> for ContextHandle {
    fn from_command(command: CommandCallMeta<'de>) -> Result<Self, serde_json::Error> {
        Ok(command.context)
    }
}

macro_rules! pass {
    ($fn:ident, $($arg:ident: $argt:ty),+) => {
        fn $fn<V : Visitor<'de>>(self, $($arg: $argt),*) -> Result<V::Value, Self::Error> {
            self.deserialize_json()?.$fn($($arg),*)
        }
    };
}

impl<'de> CommandCallMeta<'de> {
    fn deserialize_json(&self) -> serde_json::Result<&'de serde_json::Value> {
        if self.key.is_empty() {
            return Err(serde_json::Error::custom("key is empty"));
        }

        match &self.payload {
            Some(v) => match v.get(self.key) {
                Some(value) => Ok(value),
                None => Err(serde_json::Error::custom("key not found")),
            },
            None => Err(serde_json::Error::custom("payload is empty")),
        }
    }
}

impl<'de> Deserializer<'de> for CommandCallMeta<'de> {
    type Error = serde_json::Error;

    pass!(deserialize_any, visitor: V);
    pass!(deserialize_bool, visitor: V);
    pass!(deserialize_i8, visitor: V);
    pass!(deserialize_i16, visitor: V);
    pass!(deserialize_i32, visitor: V);
    pass!(deserialize_i64, visitor: V);
    pass!(deserialize_u8, visitor: V);
    pass!(deserialize_u16, visitor: V);
    pass!(deserialize_u32, visitor: V);
    pass!(deserialize_u64, visitor: V);
    pass!(deserialize_f32, visitor: V);
    pass!(deserialize_f64, visitor: V);
    pass!(deserialize_char, visitor: V);
    pass!(deserialize_str, visitor: V);
    pass!(deserialize_string, visitor: V);
    pass!(deserialize_bytes, visitor: V);
    pass!(deserialize_byte_buf, visitor: V);
    // fn deserialize_string<V>(self, _visitor: V) -> Result<V::Value, Self::Error>
    // where
    //     V: Visitor<'de> {
    //     self.deserialize_json()?.deserialize_string(_visitor)
    // }

    fn deserialize_option<V>(self, visitor: V) -> Result<V::Value, Self::Error>
    where
        V: Visitor<'de>,
    {
        if self.key.is_empty() {
            return Err(serde_json::Error::custom("key is empty"));
        }

        match &self.payload {
            Some(v) => match v.get(self.key) {
                Some(value) => value.deserialize_option(visitor),
                None => visitor.visit_none(),
            },
            None => visitor.visit_none(),
        }
    }

    pass!(deserialize_unit, visitor: V);
    pass!(deserialize_unit_struct, name: &'static str, visitor: V);
    pass!(deserialize_newtype_struct, name: &'static str, visitor: V);
    pass!(deserialize_seq, visitor: V);
    pass!(deserialize_tuple, len: usize, visitor: V);
    pass!(deserialize_tuple_struct, name: &'static str, len: usize, visitor: V);
    pass!(deserialize_map, visitor: V);
    pass!(deserialize_struct, name: &'static str, fields: &'static [&'static str], visitor: V);
    pass!(deserialize_enum, name: &'static str, variants: &'static [&'static str], visitor: V);
    pass!(deserialize_identifier, visitor: V);
    pass!(deserialize_ignored_any, visitor: V);
}
