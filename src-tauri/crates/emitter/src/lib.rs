use std::any::Any;
use std::collections::HashMap;
use std::sync::atomic::{AtomicUsize, Ordering};

struct ListenerEntry {
    id: usize,
    callback: Box<dyn Any + Send>,
}
pub struct ListenerHandle {
    event: String,
    id: usize,
}

#[derive(Default)]
pub struct Emitter {
    _next_id: AtomicUsize,
    listeners: HashMap<String, Vec<ListenerEntry>>,
}

impl Emitter {
    pub fn new() -> Self {
        Self {
            _next_id: AtomicUsize::new(0),
            listeners: HashMap::new(),
        }
    }

    pub fn emit<T>(&mut self, event: &str, data: T)
    where
        T: 'static + Clone,
    {
        if let Some(callbacks) = self.listeners.get_mut(event) {
            // 使用倒序遍历以便在回调中安全地移除监听器
            for i in (0..callbacks.len()).rev() {
                let cb = &mut callbacks[i].callback;

                // 尝试转换为正确的回调类型
                if let Some(callback) = cb.downcast_mut::<Box<dyn FnMut(T)>>() {
                    callback(data.clone());
                }
            }
        }
    }

    pub fn on<T, F>(&mut self, event: &str, callback: F) -> ListenerHandle
    where
        T: 'static,
        F: FnMut(T) + Send + 'static,
    {
        let id = self._next_id.fetch_add(1, Ordering::Relaxed);
        let event = event.to_string();
        // 明确指定回调类型
        let boxed_callback: Box<dyn FnMut(T) + Send> = Box::new(callback);
        let any_callback: Box<dyn Any + Send> = Box::new(boxed_callback);

        self.listeners
            .entry(event.clone())
            .or_insert_with(Vec::new)
            .push(ListenerEntry {
                id,
                callback: any_callback,
            });

        ListenerHandle { event, id }
    }

    pub fn off<T>(&mut self, handle: ListenerHandle)
    where
        T: 'static,
    {
        if let Some(callbacks) = self.listeners.get_mut(&handle.event) {
            if let Some(pos) = callbacks.iter().position(|entry| entry.id == handle.id) {
                callbacks.remove(pos);
            }
        }
    }

    pub fn remove_all(&mut self, event: &str) {
        self.listeners.remove(event);
    }

    pub fn has_listener(&self, event: &str) -> bool {
        self.listeners.contains_key(event)
    }

    pub fn listener_count(&self, event: &str) -> usize {
        self.listeners.get(event).map(|v| v.len()).unwrap_or(0)
    }
}

// // 使用示例
// fn main() {
//     let mut emitter = MyEmitter::new();

//     // 添加监听器并获取句柄
//     let handle1 = emitter.on("click", |data: i32| {
//         println!("Click event received with data: {}", data);
//     });

//     let handle2 = emitter.on("click", |data: i32| {
//         println!("Another click handler: {}", data);
//     });

//     // 发射事件
//     emitter.emit("click", 42);

//     // 移除特定的监听器
//     emitter.off(handle1);

//     // 再次发射事件，只有第二个监听器会触发
//     emitter.emit("click", 100);

//     // 移除所有点击事件的监听器
//     emitter.remove_all("click");

//     // 检查是否有监听器
//     println!("Has click listener: {}", emitter.has_listener("click"));
// }
