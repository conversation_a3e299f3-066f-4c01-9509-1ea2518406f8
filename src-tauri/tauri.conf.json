{"$schema": "https://schema.tauri.app/config/2", "productName": "tauri-app", "version": "0.1.0", "identifier": "com.zhangfeng.tauri-app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"create": false, "title": "tauri-app", "width": 800, "height": 600, "titleBarStyle": "Overlay", "trafficLightPosition": {"x": 20, "y": 20}, "visible": false, "hiddenTitle": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "resources": ["resources/**/*"]}}