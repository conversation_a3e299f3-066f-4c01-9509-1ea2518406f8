# SCSS Modules Integration Guide

This project has been configured to use SCSS modules for component-scoped styling. Here's how to use them:

## What's Been Set Up

1. **Vite Configuration**: Updated `vite.config.ts` to handle SCSS modules with proper naming conventions
2. **TypeScript Support**: Added type declarations for `.module.scss` files
3. **Global Variables**: Created a shared variables file for consistent theming
4. **Example Components**: Converted App component and created ExampleComponent to demonstrate usage

## File Structure

```
src/
├── styles/
│   ├── variables.scss      # Global SCSS variables
│   └── global.scss         # Global styles (non-modular)
├── types/
│   └── scss-modules.d.ts   # TypeScript declarations for SCSS modules
├── components/
│   ├── ExampleComponent.tsx
│   └── ExampleComponent.module.scss
├── App.tsx
├── App.module.scss
└── main.tsx
```

## How to Use SCSS Modules

### 1. Create a SCSS Module File

Create files with the `.module.scss` extension:

```scss
// MyComponent.module.scss
@import "../styles/variables.scss";

.container {
  padding: 1rem;
  background-color: $primary-color;
}

.title {
  font-size: 1.5rem;
  color: $text-light;
}
```

### 2. Import and Use in React Components

```tsx
// MyComponent.tsx
import React from 'react';
import styles from './MyComponent.module.scss';

const MyComponent = () => {
  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Hello World</h1>
    </div>
  );
};
```

### 3. Combining Classes

```tsx
// Combine multiple classes
<div className={`${styles.button} ${styles.primary}`}>
  Click me
</div>

// Or use a utility function
const classNames = (...classes: string[]) => classes.filter(Boolean).join(' ');

<div className={classNames(styles.button, isActive && styles.active)}>
  Dynamic classes
</div>
```

## Features

- **Scoped Styles**: All classes are automatically scoped to prevent conflicts
- **TypeScript Support**: Full IntelliSense and type checking for CSS class names
- **Global Variables**: Shared SCSS variables for consistent theming
- **Hot Reload**: Changes to SCSS files trigger hot reload during development
- **Dark Mode Support**: Built-in support for `prefers-color-scheme: dark`

## Class Naming Convention

The generated class names follow this pattern:
`[component-name]__[local-class-name]___[hash]`

For example: `App__container___2x3kl`

## Global Variables Available

Check `src/styles/variables.scss` for all available variables:
- Colors: `$primary-color`, `$text-light`, `$bg-dark`, etc.
- Typography: `$font-family`, `$font-size-base`, etc.
- Spacing: `$padding-base`, `$margin-input`, etc.
- Effects: `$box-shadow`, `$border-radius`, etc.

## Best Practices

1. **Use semantic class names** in your SCSS modules
2. **Import variables** at the top of each module file
3. **Keep global styles minimal** - most styles should be in modules
4. **Use nesting sparingly** - prefer flat class structures when possible
5. **Leverage CSS custom properties** for dynamic theming when needed

## Troubleshooting

If you encounter issues:
1. Ensure file names end with `.module.scss`
2. Check that TypeScript declarations are properly imported
3. Verify Vite configuration includes CSS modules settings
4. Make sure SASS is installed as a dev dependency
