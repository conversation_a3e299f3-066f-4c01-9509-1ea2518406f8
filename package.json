{"name": "tauri-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri dev"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-log": "~2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-window-state": "^2.4.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/node": "^24.3.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "sass": "^1.91.0", "typescript": "~5.8.3", "vite": "^7.0.4"}}