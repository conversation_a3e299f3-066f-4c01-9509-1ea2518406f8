# Draggable Area Implementation Guide

This guide explains how to implement custom draggable areas in your Tauri application that allow users to move the window by dragging specific UI elements.

## Overview

The implementation consists of two main components:

1. **DraggableArea** - A reusable component for creating any draggable region
2. **CustomTitleBar** - A complete custom title bar implementation using DraggableArea

## Components

### DraggableArea

A flexible component that makes any area draggable for window movement.

#### Usage

```tsx
import DraggableArea from './components/DraggableArea';

// Basic usage
<DraggableArea>
  <div>Drag this area to move the window</div>
</DraggableArea>

// With custom styling and height
<DraggableArea className="my-custom-class" height="60px">
  <div>Custom draggable header</div>
</DraggableArea>
```

#### Props

- `children?: React.ReactNode` - Content to display in the draggable area
- `className?: string` - Additional CSS classes
- `height?: string` - Height of the draggable area (default: "40px")

#### Features

- **Smart interaction handling**: Prevents dragging when clicking on interactive elements (buttons, inputs, etc.)
- **Visual feedback**: Changes cursor to indicate draggable state
- **Flexible positioning**: Can be positioned anywhere (fixed by default for title bars)
- **Cross-platform**: Works on Windows, macOS, and Linux

### CustomTitleBar

A complete custom title bar implementation with window controls.

#### Usage

```tsx
import CustomTitleBar from './components/CustomTitleBar';

// Basic usage
<CustomTitleBar />

// With custom title
<CustomTitleBar title="My App" />

// Without window controls (useful for macOS with native traffic lights)
<CustomTitleBar title="My App" showWindowControls={false} />
```

#### Props

- `title?: string` - Title to display in the title bar (default: "Tauri App")
- `showWindowControls?: boolean` - Whether to show minimize/maximize/close buttons (default: true)

#### Features

- **Window controls**: Minimize, maximize/restore, and close buttons
- **Platform-aware**: Automatically hides window controls on macOS (uses native traffic lights)
- **Glassmorphism design**: Modern translucent appearance with backdrop blur
- **Responsive**: Adapts to light and dark themes

## Configuration Requirements

### Tauri Configuration

Your `src-tauri/tauri.conf.json` should have these window settings:

```json
{
  "app": {
    "windows": [
      {
        "titleBarStyle": "Transparent",
        "hiddenTitle": true,
        "trafficLightPosition": {
          "x": 20,
          "y": 20
        }
      }
    ]
  }
}
```

### CSS Considerations

When using a fixed title bar, add padding to your main content:

```scss
.container {
  padding-top: calc(your-existing-padding + 40px); // Add space for title bar
}
```

## Implementation Details

### How It Works

1. **Mouse Event Handling**: The component listens for `mousedown` events
2. **Interactive Element Detection**: Checks if the clicked element is interactive (button, input, etc.)
3. **Tauri API Call**: Uses `getCurrentWindow().startDragging()` to initiate window dragging
4. **Data Attribute**: Includes `data-tauri-drag-region` for additional Tauri integration

### Key Code Snippets

#### Basic Dragging Logic

```tsx
const handleMouseDown = async (e: React.MouseEvent) => {
  // Prevent dragging on interactive elements
  const target = e.target as HTMLElement;
  if (target.tagName === 'BUTTON' || target.tagName === 'INPUT' || target.closest('button')) {
    return;
  }

  try {
    const appWindow = getCurrentWindow();
    await appWindow.startDragging();
  } catch (error) {
    console.error('Failed to start window dragging:', error);
  }
};
```

#### Window Controls Implementation

```tsx
const handleMinimize = async () => {
  const appWindow = getCurrentWindow();
  await appWindow.minimize();
};

const handleMaximize = async () => {
  const appWindow = getCurrentWindow();
  const isMaximized = await appWindow.isMaximized();
  if (isMaximized) {
    await appWindow.unmaximize();
  } else {
    await appWindow.maximize();
  }
};

const handleClose = async () => {
  const appWindow = getCurrentWindow();
  await appWindow.close();
};
```

## Styling

### DraggableArea Styles

```scss
.draggableArea {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  user-select: none;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
}
```

### Platform-Specific Considerations

```scss
// macOS traffic light spacing
.draggableArea {
  padding-left: 80px; // Space for traffic lights
}

// Hide window controls on macOS
@media (os: macos) {
  .windowControls {
    display: none;
  }
}
```

## Best Practices

1. **Interactive Elements**: Always prevent dragging on buttons, inputs, and other interactive elements
2. **Visual Feedback**: Use cursor changes to indicate draggable areas
3. **Platform Consistency**: Respect platform conventions (e.g., traffic lights on macOS)
4. **Error Handling**: Wrap Tauri API calls in try-catch blocks
5. **Accessibility**: Ensure draggable areas don't interfere with keyboard navigation

## Troubleshooting

### Common Issues

1. **Dragging doesn't work**: Ensure `titleBarStyle: "Transparent"` is set in Tauri config
2. **Interactive elements trigger drag**: Check that your event handling properly excludes interactive elements
3. **Layout issues**: Make sure to add appropriate padding for fixed title bars
4. **Window controls not working**: Verify that the Tauri window API is properly imported

### Debug Tips

- Check browser console for error messages
- Verify that `@tauri-apps/api` is properly installed
- Test on different platforms to ensure cross-platform compatibility

## Running the Example

To see the implementation in action:

```bash
npm run tauri
```

The app will show:
- A custom title bar at the top (draggable)
- Various draggable area examples
- Interactive elements that don't trigger dragging

Try dragging different areas to see how the window movement works!